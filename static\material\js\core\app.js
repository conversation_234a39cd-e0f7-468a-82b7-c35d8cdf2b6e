/**
 * Material Design App - Core Application Logic
 */

class MaterialApp {
    constructor() {
        this.isInitialized = false;
        this.components = new Map();
        this.eventListeners = new Map();
        this.config = {
            theme: localStorage.getItem('theme') || 'light',
            sidebarOpen: false,
            animations: true
        };
        
        this.init();
    }

    /**
     * Initialize the application
     */
    init() {
        if (this.isInitialized) return;
        
        console.log('Initializing Material Design App...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
        } else {
            this.onDOMReady();
        }
    }

    /**
     * Handle DOM ready event
     */
    onDOMReady() {
        this.initializeTheme();
        this.initializeComponents();
        this.bindEventListeners();
        this.hidePreloader();
        
        this.isInitialized = true;
        console.log('Material Design App initialized successfully');
        
        // Dispatch custom event
        document.dispatchEvent(new CustomEvent('app:initialized', {
            detail: { app: this }
        }));
    }

    /**
     * Initialize theme
     */
    initializeTheme() {
        document.documentElement.setAttribute('data-theme', this.config.theme);
        
        // Update theme toggle button
        const themeToggle = document.getElementById('dark-mode-toggle');
        if (themeToggle) {
            themeToggle.textContent = this.config.theme === 'dark' ? 'light_mode' : 'dark_mode';
        }
    }

    /**
     * Initialize Material Design Components
     */
    initializeComponents() {
        // Initialize MDC components
        this.initializeMDCComponents();
        
        // Initialize custom components
        this.initializeCustomComponents();
    }

    /**
     * Initialize MDC (Material Design Components) Web components
     */
    initializeMDCComponents() {
        // Initialize buttons
        const buttons = document.querySelectorAll('.mdc-button');
        buttons.forEach(button => {
            if (!button._mdcComponent) {
                button._mdcComponent = new mdc.ripple.MDCRipple(button);
            }
        });

        // Initialize FABs
        const fabs = document.querySelectorAll('.mdc-fab');
        fabs.forEach(fab => {
            if (!fab._mdcComponent) {
                fab._mdcComponent = new mdc.ripple.MDCRipple(fab);
            }
        });

        // Initialize text fields
        const textFields = document.querySelectorAll('.mdc-text-field');
        textFields.forEach(textField => {
            if (!textField._mdcComponent) {
                textField._mdcComponent = new mdc.textField.MDCTextField(textField);
            }
        });

        // Initialize circular progress
        const progressBars = document.querySelectorAll('.mdc-circular-progress');
        progressBars.forEach(progress => {
            if (!progress._mdcComponent) {
                progress._mdcComponent = new mdc.circularProgress.MDCCircularProgress(progress);
            }
        });
    }

    /**
     * Initialize custom components
     */
    initializeCustomComponents() {
        // Initialize sidebar
        this.components.set('sidebar', new SidebarComponent());
        
        // Initialize search
        this.components.set('search', new SearchComponent());
        
        // Initialize modals
        this.components.set('modal', new ModalComponent());
        
        // Initialize bottom sheet
        this.components.set('bottomSheet', new BottomSheetComponent());
    }

    /**
     * Bind global event listeners
     */
    bindEventListeners() {
        // Theme toggle
        this.addEventListener('dark-mode-toggle', 'click', () => this.toggleTheme());
        
        // Menu button
        this.addEventListener('menu-btn', 'click', () => this.toggleSidebar());
        
        // Mobile search
        this.addEventListener('mobile-search-btn', 'click', () => this.openMobileSearch());
        
        // Filter button
        this.addEventListener('filter-btn', 'click', () => this.openFilters());
        
        // QR Scanner
        this.addEventListener('qr-fab', 'click', () => this.openQRScanner());
        
        // Add student
        this.addEventListener('add-student-fab', 'click', () => this.addStudent());
        this.addEventListener('add-student-btn', 'click', () => this.addStudent());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // Handle clicks outside components
        document.addEventListener('click', (e) => this.handleOutsideClick(e));
    }

    /**
     * Add event listener with cleanup tracking
     */
    addEventListener(elementId, event, handler) {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener(event, handler);
            
            // Track for cleanup
            if (!this.eventListeners.has(elementId)) {
                this.eventListeners.set(elementId, []);
            }
            this.eventListeners.get(elementId).push({ event, handler });
        }
    }

    /**
     * Toggle theme between light and dark
     */
    toggleTheme() {
        this.config.theme = this.config.theme === 'light' ? 'dark' : 'light';
        localStorage.setItem('theme', this.config.theme);
        this.initializeTheme();
        
        // Animate theme transition
        document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    /**
     * Toggle sidebar
     */
    toggleSidebar() {
        const sidebar = this.components.get('sidebar');
        if (sidebar) {
            sidebar.toggle();
        }
    }

    /**
     * Open mobile search
     */
    openMobileSearch() {
        const search = this.components.get('search');
        if (search) {
            search.openMobile();
        }
    }

    /**
     * Open filters
     */
    openFilters() {
        const filterOffcanvas = document.getElementById('filter-offcanvas-overlay');
        if (filterOffcanvas) {
            filterOffcanvas.classList.add('active');
        }
    }

    /**
     * Open QR scanner
     */
    openQRScanner() {
        const qrScanner = document.getElementById('qr-scanner-overlay');
        if (qrScanner) {
            qrScanner.classList.add('active');
        }
    }

    /**
     * Add student
     */
    addStudent() {
        const modal = this.components.get('modal');
        if (modal) {
            modal.open('Add Student', 'student-form');
        }
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            this.openMobileSearch();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            this.closeAllOverlays();
        }
    }

    /**
     * Handle clicks outside components
     */
    handleOutsideClick(e) {
        // Close dropdowns, modals, etc. when clicking outside
        const overlays = document.querySelectorAll('.overlay.active, .offcanvas-overlay.active');
        overlays.forEach(overlay => {
            if (!overlay.contains(e.target)) {
                overlay.classList.remove('active');
            }
        });
    }

    /**
     * Close all overlays
     */
    closeAllOverlays() {
        const overlays = document.querySelectorAll('.overlay, .offcanvas-overlay, .modal-overlay');
        overlays.forEach(overlay => {
            overlay.classList.remove('active');
        });
    }

    /**
     * Hide preloader
     */
    hidePreloader() {
        const preloader = document.getElementById('page-preloader');
        if (preloader) {
            setTimeout(() => {
                preloader.style.opacity = '0';
                setTimeout(() => {
                    preloader.style.display = 'none';
                }, 300);
            }, 500);
        }
    }

    /**
     * Show loading overlay
     */
    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loading-overlay');
        const text = document.getElementById('loading-text');
        
        if (overlay) {
            if (text) text.textContent = message;
            overlay.classList.add('active');
        }
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }

    /**
     * Cleanup resources
     */
    destroy() {
        // Remove event listeners
        this.eventListeners.forEach((listeners, elementId) => {
            const element = document.getElementById(elementId);
            if (element) {
                listeners.forEach(({ event, handler }) => {
                    element.removeEventListener(event, handler);
                });
            }
        });
        
        // Destroy components
        this.components.forEach(component => {
            if (component.destroy) {
                component.destroy();
            }
        });
        
        this.isInitialized = false;
    }
}

// Initialize app when script loads
window.MaterialApp = MaterialApp;
window.app = new MaterialApp();
