
<div x-show="activeNav === 'students'"
    x-transition:enter="fade-scale-enter"
    x-transition:enter-start=""
    x-transition:enter-end="fade-scale-enter-active"
    x-transition:leave="fade-scale-leave"
    x-transition:leave-start=""
    x-transition:leave-end="fade-scale-leave-active"
>
    <!-- Quick Filter Chips -->
    <div class="quick-filter-chips">
        <button class="quick-filter-chip active" data-filter="all" @click="setQuickFilter('all')">
            <span class="material-icons">group</span>
            <span>All</span>
        </button>
        <button class="quick-filter-chip" data-filter="paid" @click="setQuickFilter('paid')">
            <span class="material-icons">paid</span>
            <span>Paid</span>
        </button>
        <button class="quick-filter-chip" data-filter="no-payments" @click="setQuickFilter('no-payments')">
            <span class="material-icons">money_off</span>
            <span>No Pay</span>
        </button>
        <button class="quick-filter-chip" data-filter="all-paid" @click="setQuickFilter('all-paid')">
            <span class="material-icons">check_circle</span>
            <span>Full Paid</span>
        </button>
        <button class="quick-filter-chip" data-filter="enrolled-today" @click="setQuickFilter('enrolled-today')">
            <span class="material-icons">today</span>
            <span>Today</span>
        </button>
    </div>

    <!-- Filter Chips Container -->
    <div class="filter-chips-container" id="filter-chips-container">
        <!-- Filter chips will be dynamically added here -->
    </div>

    <!-- Data Table (Desktop) -->
    <div class="data-table-container">
        <!-- Targeted Loading Overlay for Table -->
        <div class="target-loading-overlay" id="table-loading-overlay">
            <div class="target-loading-content">
                <div class="simple-spinner">
                    <svg class="spinner-svg" viewBox="0 0 50 50">
                        <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#1976d2" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                            <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                            <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                        </circle>
                    </svg>
                </div>
                <div class="target-loading-text" id="table-loading-text">Loading...</div>
            </div>
        </div>

        <!-- Table Controls -->
        <div class="table-controls">
            <div class="search-container">
                <span class="search-label">Search:</span>
                <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--with-leading-icon mdc-small-input mdc-search-input">
                    <span class="mdc-text-field__icon mdc-text-field__icon--leading material-icons">search</span>
                    <input type="text" class="mdc-text-field__input" id="table-search" placeholder="Search students..." x-model="searchQuery">
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__notch"></div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>
            </div>
            <div class="per-page-container">
                <span class="per-page-label">Rows per page:</span>
                <div class="mdc-select mdc-select--outlined mdc-small-input" id="per-page-select">
                    <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                        <span class="mdc-select__selected-text-container">
                            <span class="mdc-select__selected-text" x-text="perPage"></span>
                        </span>
                        <span class="mdc-select__dropdown-icon">
                            <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                                <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                                <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                            </svg>
                        </span>
                        <div class="mdc-notched-outline">
                            <div class="mdc-notched-outline__leading"></div>
                            <div class="mdc-notched-outline__trailing"></div>
                        </div>
                    </div>
                    <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                        <ul class="mdc-deprecated-list" role="listbox">
                            <li class="mdc-deprecated-list-item" data-value="5" role="option">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">5</span>
                            </li>
                            <li class="mdc-deprecated-list-item mdc-deprecated-list-item--selected" data-value="10" role="option" aria-selected="true">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">10</span>
                            </li>
                            <li class="mdc-deprecated-list-item" data-value="25" role="option">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">25</span>
                            </li>
                            <li class="mdc-deprecated-list-item" data-value="50" role="option">
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">50</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- MDC Data Table -->
        <div class="mdc-data-table" x-show="filteredData.length > 0">
            <div class="mdc-data-table__table-container">
                <table class="mdc-data-table__table" aria-label="Students list">
                    <thead>
                        <tr class="mdc-data-table__header-row">
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                <div class="mdc-checkbox mdc-data-table__header-row-checkbox" :class="{ 'mdc-checkbox--selected': allVisibleSelected }">
                                    <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-checkbox" aria-label="Toggle all rows selected"
                                            :checked="allVisibleSelected"
                                            :indeterminate="someVisibleSelected && !allVisibleSelected"
                                            @change="selectAllStudents()">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Photo</th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                :class="getSortClass('name')"
                                role="columnheader" scope="col" data-column="name"
                                @click="sortData('name')">
                                Full Name
                                <span class="material-icons sort-icon" x-text="getSortIcon('name')"></span>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                :class="getSortClass('id')"
                                role="columnheader" scope="col" data-column="id"
                                @click="sortData('id')">
                                Student ID
                                <span class="material-icons sort-icon" x-text="getSortIcon('id')"></span>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                :class="getSortClass('gender')"
                                role="columnheader" scope="col" data-column="gender"
                                @click="sortData('gender')">
                                Gender
                                <span class="material-icons sort-icon" x-text="getSortIcon('gender')"></span>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                :class="getSortClass('levelFr')"
                                role="columnheader" scope="col" data-column="levelFr"
                                @click="sortData('levelFr')">
                                Grade (FR)
                                <span class="material-icons sort-icon" x-text="getSortIcon('levelFr')"></span>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                :class="getSortClass('levelAr')"
                                role="columnheader" scope="col" data-column="levelAr"
                                @click="sortData('levelAr')">
                                Grade (AR)
                                <span class="material-icons sort-icon" x-text="getSortIcon('levelAr')"></span>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable"
                                :class="getSortClass('status')"
                                role="columnheader" scope="col" data-column="status"
                                @click="sortData('status')">
                                Status
                                <span class="material-icons sort-icon" x-text="getSortIcon('status')"></span>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column"
                                :class="getSortClass('fees')"
                                role="columnheader" scope="col" data-column="fees"
                                @click="sortData('fees')">
                                Fees
                                <span class="material-icons sort-icon" x-text="getSortIcon('fees')"></span>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column"
                                :class="getSortClass('amountPaid')"
                                role="columnheader" scope="col" data-column="amountPaid"
                                @click="sortData('amountPaid')">
                                Amount Paid
                                <span class="material-icons sort-icon" x-text="getSortIcon('amountPaid')"></span>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column"
                                :class="getSortClass('remaining')"
                                role="columnheader" scope="col" data-column="remaining"
                                @click="sortData('remaining')">
                                Remaining
                                <span class="material-icons sort-icon" x-text="getSortIcon('remaining')"></span>
                            </th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="mdc-data-table__content" id="table-body">
                        <!-- Alpine.js Table Rows Template -->
                        <template x-if="paginatedData.length === 0">
                            <tr class="mdc-data-table__row">
                                <td class="mdc-data-table__cell" colspan="12" style="text-align: center; padding: 48px;">
                                    <div style="color: #757575;">
                                        <span class="material-icons" style="font-size: 48px; margin-bottom: 16px; display: block;">search_off</span>
                                        No students found
                                    </div>
                                </td>
                            </tr>
                        </template>

                        <template x-for="student in paginatedData" :key="student.id">
                            <tr class="mdc-data-table__row table-row"
                                :class="{ 'mdc-data-table__row--selected': isStudentSelected(student.id) }"
                                :data-student-id="student.id"
                                @click="handleTableRowClick($event, student)">
                                <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                    <div class="mdc-checkbox mdc-data-table__row-checkbox" :class="{ 'mdc-checkbox--selected': isStudentSelected(student.id) }">
                                        <input type="checkbox" class="mdc-checkbox__native-control row-checkbox" aria-label="Select row"
                                                :checked="isStudentSelected(student.id)"
                                                @change="toggleStudentSelection(student.id)"
                                                @click.stop>
                                        <div class="mdc-checkbox__background">
                                            <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                                <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                            </svg>
                                            <div class="mdc-checkbox__mixedmark"></div>
                                        </div>
                                        <div class="mdc-checkbox__ripple"></div>
                                    </div>
                                </td>
                                <td class="mdc-data-table__cell">
                                    <div class="student-photo" :style="`background-image: url('${student.photo}')`"></div>
                                </td>
                                <th class="mdc-data-table__cell" scope="row" x-text="student.name"></th>
                                <td class="mdc-data-table__cell" x-text="student.id"></td>
                                <td class="mdc-data-table__cell" x-text="student.gender"></td>
                                <td class="mdc-data-table__cell" x-text="student.levelFr"></td>
                                <td class="mdc-data-table__cell" x-text="student.levelAr"></td>
                                <td class="mdc-data-table__cell" x-text="student.status"></td>
                                <td class="mdc-data-table__cell numeric-cell" x-text="student.totalAmount.toLocaleString()"></td>
                                <td class="mdc-data-table__cell numeric-cell" x-text="student.paidAmount.toLocaleString()"></td>
                                <td class="mdc-data-table__cell numeric-cell" x-text="student.remainingAmount.toLocaleString()"></td>
                                <td class="mdc-data-table__cell">
                                    <div class="table-actions">
                                        <button class="action-btn edit-btn" title="Edit Student" @click.stop="console.log('Edit student:', student.name)">
                                            <span class="material-icons">edit</span>
                                        </button>
                                        <button class="action-btn delete-btn" title="Delete Student" @click.stop="deleteStudent(student)">
                                            <span class="material-icons">delete</span>
                                        </button>
                                        <button class="action-btn more-btn" title="More Options" @click.stop="handleMoreAction($event, student)">
                                            <span class="material-icons">more_vert</span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
            <!-- Pagination -->
            <div class="table-pagination">
                <div class="pagination-info" id="pagination-info">
                    <span x-text="`Showing ${((currentPage - 1) * perPage) + 1}-${Math.min(currentPage * perPage, filteredData.length)} of ${filteredData.length} students`"></span>
                </div>
                <div class="pagination-controls">
                    <button class="mdc-icon-button"
                            :disabled="currentPage <= 1"
                            @click="prevPage()">
                        <span class="material-icons">chevron_left</span>
                    </button>
                    <div class="pagination-pages" id="pagination-pages">
                        <template x-for="page in visiblePages" :key="page">
                            <button class="pagination-page"
                                    :class="{ 'active': page === currentPage, 'ellipsis': page === '...' }"
                                    :disabled="page === '...'"
                                    @click="page !== '...' && goToPage(page)"
                                    x-text="page"></button>
                        </template>
                    </div>
                    <button class="mdc-icon-button"
                            :disabled="currentPage >= totalPages"
                            @click="nextPage()">
                        <span class="material-icons">chevron_right</span>
                    </button>
                </div>
            </div>
        </div>

    </div>

    <!-- No Data Found Component -->
    <div class="no-data-found" x-show="filteredData.length === 0">
        <div class="no-data-content">
            <span class="material-icons no-data-icon">search_off</span>
            <div class="no-data-title">No students found</div>
            <div class="no-data-message">Try adjusting your search or filter criteria</div>
            <button class="mdc-button mdc-button--outlined no-data-action" @click="clearAllFilters()">
                <span class="mdc-button__ripple"></span>
                <span class="mdc-button__label">Clear Filters</span>
            </button>
        </div>
    </div>

    <!-- Students List (Mobile) -->
    <div class="students-list">
        <!-- Targeted Loading Overlay for Mobile List -->
        <div class="target-loading-overlay" id="list-loading-overlay">
            <div class="target-loading-content">
                <div class="simple-spinner">
                    <svg class="spinner-svg" viewBox="0 0 50 50">
                        <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#1976d2" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                            <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                            <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                        </circle>
                    </svg>
                </div>
                <div class="target-loading-text" id="list-loading-text">Loading...</div>
            </div>
        </div>

        <!-- Alpine.js Mobile Students Template -->
        <template x-for="(student, index) in mobileDisplayData" :key="student.id">
            <div class="student-item"
                    :data-student-id="student.id"
                    @click="showBottomSheet({
                        id: student.id,
                        name: student.name,
                        level: student.level,
                        details: `Born: ${student.birth} • Student ID: ${student.id}`,
                        photo: `url('${student.photo}')`
                    })">
                <div class="student-photo" :style="`background-image: url('${student.photo}')`"></div>
                <div class="student-info">
                    <div class="student-header">
                        <div class="student-name" x-text="student.name"></div>
                        <div class="student-id" x-text="student.id"></div>
                    </div>
                    <div class="student-name-ar" x-text="student.nameAr"></div>
                    <div class="student-birth-date" x-text="formatBirthDate(student.birth) + ' • ' + (student.birthPlace && student.birthPlace.length > 16 ? student.birthPlace.substring(0, 16) + '...' : student.birthPlace)"></div>
                    <div class="student-grades">
                        <span class="grade-fr" x-text="student.levelFr"></span>
                        <span class="grade-separator">•</span>
                        <span class="grade-ar" x-text="student.levelAr"></span>
                    </div>
                </div>
                <div class="student-actions">
                    <button class="edit-btn" title="Edit Student" @click.stop="console.log('Edit student:', student.name)">
                        <span class="material-icons">edit</span>
                    </button>
                    <button class="delete-btn" title="Delete Student" @click.stop="deleteStudent(student)">
                        <span class="material-icons">delete</span>
                    </button>
                </div>
            </div>
        </template>

        <!-- Loading More Spinner Before New Content -->
        <div class="loading-more-spinner" x-show="isLoadingMore && hasMoreMobileData">
            <div class="spinner-container">
                <div class="mdc-circular-progress mdc-circular-progress--small mdc-circular-progress--indeterminate" role="progressbar" aria-label="Loading more students...">
                    <div class="mdc-circular-progress__determinate-container">
                        <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <circle class="mdc-circular-progress__determinate-track" cx="12" cy="12" r="8.5" stroke-width="2.5"/>
                            <circle class="mdc-circular-progress__determinate-circle" cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="53.407" stroke-width="2.5"/>
                        </svg>
                    </div>
                    <div class="mdc-circular-progress__indeterminate-container">
                        <div class="mdc-circular-progress__spinner-layer">
                            <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                                <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="26.704" stroke-width="2.5"/>
                                </svg>
                            </div>
                            <div class="mdc-circular-progress__gap-patch">
                                <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="26.704" stroke-width="2"/>
                                </svg>
                            </div>
                            <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right">
                                <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="8.5" stroke-dasharray="53.407" stroke-dashoffset="26.704" stroke-width="2.5"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="loading-more-text">Loading more students...</div>
        </div>

        <!-- End of Results Message -->
        <div class="end-of-results" x-show="!hasMoreMobileData && mobileDisplayData.length > 0">
            <div class="end-of-results-content">
                <span class="material-icons">check_circle</span>
                <div class="end-of-results-text">You've reached the end of the list</div>
                <div class="end-of-results-count" x-text="`Showing all ${filteredData.length} students`"></div>
            </div>
        </div>
    </div>
</div>

<div class="bulk-action-bar" :class="{ 'visible': selectedStudentIds.size > 0 }" x-transition>
    <div class="bulk-selected-count" x-text="selectedStudentIds.size === 1 ? '1 selected' : `${selectedStudentIds.size} selected`"></div>
    <div class="bulk-actions">
        <button class="bulk-action-btn archive" @click="archiveSelectedStudents()">
            <span class="material-icons">archive</span>
            <span>Archive</span>
        </button>
        <button class="bulk-action-btn delete" @click="deleteSelectedStudents()">
            <span class="material-icons">delete</span>
            <span>Delete</span>
        </button>
    </div>
</div>
