<div x-show="activeNav === 'levels'"
    x-data="{ activeTab: 'french' }"
    x-transition:enter="fade-scale-enter"
    x-transition:enter-start=""
    x-transition:enter-end="fade-scale-enter-active"
    x-transition:leave="fade-scale-leave"
    x-transition:leave-start=""
    x-transition:leave-end="fade-scale-leave-active"
>
    <!-- Levels Tabs - Material Design M2 -->
    <div class="mdc-tab-bar" role="tablist">
        <div class="mdc-tab-scroller">
            <div class="mdc-tab-scroller__scroll-area">
                <div class="mdc-tab-scroller__scroll-content">
                    <button class="mdc-tab" :class="{ 'mdc-tab--active': activeTab === 'french' }"
                            role="tab" :aria-selected="activeTab === 'french'"
                            @click="activeTab = 'french'">
                        <span class="mdc-tab__content">
                            <span class="mdc-tab__icon material-icons">translate</span>
                            <span class="mdc-tab__text-label">French Levels</span>
                        </span>
                        <span class="mdc-tab-indicator" :class="{ 'mdc-tab-indicator--active': activeTab === 'french' }">
                            <span class="mdc-tab-indicator__content mdc-tab-indicator__content--underline"></span>
                        </span>
                        <span class="mdc-tab__ripple"></span>
                    </button>
                    <button class="mdc-tab" :class="{ 'mdc-tab--active': activeTab === 'arabic' }"
                            role="tab" :aria-selected="activeTab === 'arabic'"
                            @click="activeTab = 'arabic'">
                        <span class="mdc-tab__content">
                            <span class="mdc-tab__icon material-icons">language</span>
                            <span class="mdc-tab__text-label">Arabic Levels</span>
                        </span>
                        <span class="mdc-tab-indicator" :class="{ 'mdc-tab-indicator--active': activeTab === 'arabic' }">
                            <span class="mdc-tab-indicator__content mdc-tab-indicator__content--underline"></span>
                        </span>
                        <span class="mdc-tab__ripple"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- French Levels Table -->
    <div class="levels-content" x-show="activeTab === 'french'" id="french-levels">

        <!-- French Levels Data Table -->
        <div class="mdc-data-table">
            <div class="mdc-data-table__table-container">
                <table class="mdc-data-table__table" aria-label="French levels list">
                    <thead>
                        <tr class="mdc-data-table__header-row">
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-french-levels" aria-label="Toggle all rows selected">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable" role="columnheader" scope="col">
                                Level Name
                                <span class="material-icons sort-icon">arrow_upward</span>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column" role="columnheader" scope="col">
                                Students Count
                                <span class="material-icons sort-icon">unfold_more</span>
                            </th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="mdc-data-table__content">
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </td>
                            <th class="mdc-data-table__cell" scope="row">6ème</th>
                            <td class="mdc-data-table__cell numeric-cell">142</td>
                            <td class="mdc-data-table__cell">
                                <div class="table-actions">
                                    <button class="action-btn edit-btn" title="Edit Level">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Level">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </td>
                            <th class="mdc-data-table__cell" scope="row">5ème</th>
                            <td class="mdc-data-table__cell numeric-cell">138</td>
                            <td class="mdc-data-table__cell">
                                <div class="table-actions">
                                    <button class="action-btn edit-btn" title="Edit Level">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Level">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </td>
                            <th class="mdc-data-table__cell" scope="row">4ème</th>
                            <td class="mdc-data-table__cell numeric-cell">156</td>
                            <td class="mdc-data-table__cell">
                                <div class="table-actions">
                                    <button class="action-btn edit-btn" title="Edit Level">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Level">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </td>
                            <th class="mdc-data-table__cell" scope="row">3ème</th>
                            <td class="mdc-data-table__cell numeric-cell">134</td>
                            <td class="mdc-data-table__cell">
                                <div class="table-actions">
                                    <button class="action-btn edit-btn" title="Edit Level">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Level">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Arabic Levels Table -->
    <div class="levels-content" x-show="activeTab === 'arabic'" id="arabic-levels">

        <!-- Arabic Levels Data Table -->
        <div class="mdc-data-table">
            <div class="mdc-data-table__table-container">
                <table class="mdc-data-table__table" aria-label="Arabic levels list">
                    <thead>
                        <tr class="mdc-data-table__header-row">
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox" role="columnheader" scope="col">
                                <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" id="select-all-arabic-levels" aria-label="Toggle all rows selected">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable" role="columnheader" scope="col">
                                Level Name
                                <span class="material-icons sort-icon">arrow_upward</span>
                            </th>
                            <th class="mdc-data-table__header-cell mdc-data-table__header-cell--sortable numeric-column" role="columnheader" scope="col">
                                Students Count
                                <span class="material-icons sort-icon">unfold_more</span>
                            </th>
                            <th class="mdc-data-table__header-cell" role="columnheader" scope="col">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="mdc-data-table__content">
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </td>
                            <th class="mdc-data-table__cell" scope="row">الصف السادس</th>
                            <td class="mdc-data-table__cell numeric-cell">142</td>
                            <td class="mdc-data-table__cell">
                                <div class="table-actions">
                                    <button class="action-btn edit-btn" title="Edit Level">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Level">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </td>
                            <th class="mdc-data-table__cell" scope="row">الصف الخامس</th>
                            <td class="mdc-data-table__cell numeric-cell">138</td>
                            <td class="mdc-data-table__cell">
                                <div class="table-actions">
                                    <button class="action-btn edit-btn" title="Edit Level">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Level">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </td>
                            <th class="mdc-data-table__cell" scope="row">الصف الرابع</th>
                            <td class="mdc-data-table__cell numeric-cell">156</td>
                            <td class="mdc-data-table__cell">
                                <div class="table-actions">
                                    <button class="action-btn edit-btn" title="Edit Level">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Level">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr class="mdc-data-table__row">
                            <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                                    <input type="checkbox" class="mdc-checkbox__native-control" aria-label="Select row">
                                    <div class="mdc-checkbox__background">
                                        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                            <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                        </svg>
                                        <div class="mdc-checkbox__mixedmark"></div>
                                    </div>
                                    <div class="mdc-checkbox__ripple"></div>
                                </div>
                            </td>
                            <th class="mdc-data-table__cell" scope="row">الصف الثالث</th>
                            <td class="mdc-data-table__cell numeric-cell">134</td>
                            <td class="mdc-data-table__cell">
                                <div class="table-actions">
                                    <button class="action-btn edit-btn" title="Edit Level">
                                        <span class="material-icons">edit</span>
                                    </button>
                                    <button class="action-btn delete-btn" title="Delete Level">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>