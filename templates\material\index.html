{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Students - Management System</title>
    
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">


    <!-- HTML5 QR Code Scanner CSS -->
    <style>
        #qr-reader {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }

        #qr-reader__dashboard_section_csr {
            display: none !important;
        }

        #qr-reader__camera_selection {
            margin-bottom: 16px;
        }

        #qr-reader__scan_region {
            border-radius: 8px;
            overflow: hidden;
        }

        #qr-reader__scan_region video {
            border-radius: 8px;
        }

 .fade-scale-enter {
    transition: opacity 0.2s ease-out, transform 0.2s ease-out;
    opacity: 0;
    transform: scale(0.95);
  }
  .fade-scale-enter-active {
    opacity: 1;
    transform: scale(1);
  }
  .fade-scale-leave {
    transition: opacity 0.2s ease-in, transform 0.2s ease-in;
    opacity: 1;
    transform: scale(1);
  }
  .fade-scale-leave-active {
    opacity: 0;
    transform: scale(0.95);
  }
    </style>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/material/styles.css' %}">
</head>
<body x-data="{activeNav: 'home', pageTitle: 'Home'}">
    {% include 'material/components/preloader.html' %}

    {% include 'material/components/app_bar.html' %}
    
    <!-- Sidebar -->
    {% include 'material/components/sidebar.html' %}
    <!-- Main Content -->
    <div class="main-content" x-data="studentsApp()">
        <div class="content-header">
            <span class="material-icons">arrow_back</span>
            <h2 class="page-title">
                <span x-text="pageTitle"></span>
                <span class="page-title-count" x-show="activeNav === 'students'" x-text="`(${filteredData.length})`"></span>
            </h2>
            <div class="actions" x-show="activeNav === 'students'">
                <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons" style="pointer-events: none;">add</span>
                    <span class="mdc-button__label">Add Student</span>
                </button>
                <span class="material-icons hidden" title="Export to Excel">description</span>
                <span class="material-icons hidden" title="Import Data">file_upload</span>
                <span class="material-icons hidden" title="Toggle View">view_module</span>
                <span class="material-icons search-icon-mobile" title="Search" id="mobile-search-btn">search</span>
                <span class="material-icons" title="Filter" id="filter-btn">filter_list</span>
            </div>
            <div class="actions" x-show="activeNav === 'levels'">
                <button class="mdc-button mdc-button--raised add-btn">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons" style="pointer-events: none;">add</span>
                    <!-- <span class="mdc-button__label">Add Level</span> -->
                </button>
                <button class="mdc-button mdc-button--outlined">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons" style="pointer-events: none;">file_download</span>
                    <span class="mdc-button__label">Export</span>
                </button>
            </div>
        </div>
        {% include 'material/dashboard.html' %}
        {% include 'material/levels.html' %}
        {% include 'material/students.html' %}

    </div>

    {% include 'material/components/bottom_nav.html' %}
    {% include 'material/components/fab.html' %}
    {% include 'material/components/modals.html' %}

    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Flatpickr JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <!-- Material Design JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>

    <!-- HTML5 QR Code Scanner -->
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

    <!-- Alpine.js -->

    <!-- QR Scanner JavaScript -->
    <script src="{% static 'material/js/qr-scanner.js' %}"></script>

    <!-- Custom JavaScript -->
    <script src="{% static 'material/js/script.js' %}"></script>
    <script src="{% static 'material/js/htmx.min.js' %}"></script>
</body>
</html>