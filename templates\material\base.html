{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Student Management System{% endblock %}</title>
    
    <!-- Material Design CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.css" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    {% block extra_css %}
    <!-- QR Scanner Styles -->
    <style>
        #qr-reader {
            width: 100%;
            max-width: 500px;
            margin: 0 auto;
        }
        #qr-reader__dashboard_section_csr {
            display: none !important;
        }
        #qr-reader__camera_selection {
            margin-bottom: 16px;
        }
        #qr-reader__scan_region {
            border-radius: 8px;
            overflow: hidden;
        }
        #qr-reader__scan_region video {
            border-radius: 8px;
        }
        .fade-scale-enter {
            transition: opacity 0.2s ease-out, transform 0.2s ease-out;
            opacity: 0;
            transform: scale(0.95);
        }
        .fade-scale-enter-active {
            opacity: 1;
            transform: scale(1);
        }
        .fade-scale-leave {
            transition: opacity 0.2s ease-in, transform 0.2s ease-in;
            opacity: 1;
            transform: scale(1);
        }
        .fade-scale-leave-active {
            opacity: 0;
            transform: scale(0.95);
        }
    </style>
    {% endblock %}

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/material/main.css' %}">
</head>
<body {% block body_attrs %}x-data="{activeNav: 'home', pageTitle: 'Home'}"{% endblock %}>
    
    <!-- Page Preloader -->
    {% include 'material/components/preloader.html' %}

    <!-- App Bar -->
    {% include 'material/components/app_bar.html' %}
    
    <!-- Sidebar -->
    {% include 'material/components/sidebar.html' %}

    <!-- Main Content -->
    <div class="main-content" {% block main_content_attrs %}x-data="studentsApp()" x-init="init()"{% endblock %}>
        {% block content %}
        <!-- Content Header -->
        <div class="content-header">
            <span class="material-icons">arrow_back</span>
            <h2 class="page-title">
                <span x-text="pageTitle"></span>
                <span class="page-title-count" x-show="activeNav === 'students'" x-text="`(${filteredData.length})`"></span>
            </h2>
            <div class="actions" x-show="activeNav === 'students'">
                <button class="mdc-button mdc-button--raised add-btn" id="add-student-btn">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons" style="pointer-events: none;">add</span>
                    <span class="mdc-button__label">Add Student</span>
                </button>
                <span class="material-icons search-icon-mobile" title="Search" id="mobile-search-btn">search</span>
                <span class="material-icons" title="Filter" id="filter-btn">filter_list</span>
            </div>
        </div>

        <!-- Dashboard Section -->
        {% include 'material/sections/dashboard.html' %}

        <!-- Students Section -->
        {% include 'material/sections/students_list.html' %}

        <!-- Levels Section -->
        {% include 'material/sections/levels.html' %}
        {% endblock %}
    </div>

    <!-- Bottom App Bar (Mobile) -->
    {% include 'material/components/bottom_nav.html' %}

    <!-- Floating Action Buttons -->
    {% block fab %}
    {% include 'material/components/fab.html' %}
    {% endblock %}

    <!-- Modals and Overlays -->
    {% include 'material/components/modals.html' %}

    <!-- Scripts -->
    {% block scripts %}
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/material-components-web/14.0.0/material-components-web.min.js"></script>
    <script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>

    <!-- Core App Scripts -->
    <script src="{% static 'material/js/components/sidebar.js' %}"></script>
    <script src="{% static 'material/js/core/app.js' %}"></script>

    <!-- Feature Scripts -->
    <script src="{% static 'material/js/qr-scanner.js' %}"></script>
    <script src="{% static 'material/js/script.js' %}"></script>
    <script src="{% static 'material/js/htmx.min.js' %}"></script>
    {% endblock %}

    {% block extra_scripts %}
    {% endblock %}
</body>
</html>
