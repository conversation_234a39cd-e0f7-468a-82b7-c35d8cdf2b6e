/**
 * Sidebar Component
 */

class SidebarComponent {
    constructor() {
        this.sidebar = document.querySelector('.sidebar');
        this.overlay = document.querySelector('.sidebar-overlay');
        this.isOpen = false;
        this.isMobile = window.innerWidth < 1024;
        
        this.init();
    }

    init() {
        if (!this.sidebar) return;
        
        this.bindEvents();
        this.handleResize();
        
        // Listen for window resize
        window.addEventListener('resize', () => this.handleResize());
    }

    bindEvents() {
        // Close button
        const closeBtn = this.sidebar.querySelector('.sidebar-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.close());
        }

        // Overlay click
        if (this.overlay) {
            this.overlay.addEventListener('click', () => this.close());
        }

        // Navigation items
        const navItems = this.sidebar.querySelectorAll('.sidebar-nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => this.handleNavClick(e, item));
        });
    }

    handleNavClick(e, item) {
        e.preventDefault();
        
        // Remove active class from all items
        const allItems = this.sidebar.querySelectorAll('.sidebar-nav-item');
        allItems.forEach(navItem => navItem.classList.remove('active'));
        
        // Add active class to clicked item
        item.classList.add('active');
        
        // Get navigation target
        const target = item.getAttribute('data-nav');
        if (target) {
            this.navigateTo(target);
        }
        
        // Close sidebar on mobile after navigation
        if (this.isMobile) {
            this.close();
        }
    }

    navigateTo(target) {
        // Dispatch navigation event
        document.dispatchEvent(new CustomEvent('sidebar:navigate', {
            detail: { target }
        }));
        
        // Update Alpine.js state if available
        if (window.Alpine) {
            const mainContent = document.querySelector('[x-data]');
            if (mainContent && mainContent._x_dataStack) {
                const data = mainContent._x_dataStack[0];
                if (data.activeNav) {
                    data.activeNav = target;
                }
                if (data.pageTitle) {
                    data.pageTitle = this.getPageTitle(target);
                }
            }
        }
    }

    getPageTitle(target) {
        const titles = {
            'home': 'Dashboard',
            'students': 'Students',
            'levels': 'Grade Levels',
            'attendance': 'Attendance',
            'payments': 'Payments',
            'reports': 'Reports',
            'settings': 'Settings'
        };
        return titles[target] || 'Dashboard';
    }

    open() {
        if (!this.sidebar) return;
        
        this.isOpen = true;
        this.sidebar.classList.add('open');
        
        if (this.overlay) {
            this.overlay.classList.add('active');
        }
        
        // Prevent body scroll on mobile
        if (this.isMobile) {
            document.body.style.overflow = 'hidden';
        }
        
        // Dispatch event
        document.dispatchEvent(new CustomEvent('sidebar:opened'));
    }

    close() {
        if (!this.sidebar) return;
        
        this.isOpen = false;
        this.sidebar.classList.remove('open');
        
        if (this.overlay) {
            this.overlay.classList.remove('active');
        }
        
        // Restore body scroll
        document.body.style.overflow = '';
        
        // Dispatch event
        document.dispatchEvent(new CustomEvent('sidebar:closed'));
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth < 1024;
        
        // If switching from mobile to desktop, close sidebar
        if (wasMobile && !this.isMobile && this.isOpen) {
            this.close();
        }
        
        // Auto-open on desktop if not explicitly closed
        if (!this.isMobile && !this.isOpen) {
            // Don't auto-open, let user control
        }
    }

    destroy() {
        // Remove event listeners
        window.removeEventListener('resize', this.handleResize);
        
        // Clean up
        this.sidebar = null;
        this.overlay = null;
    }
}

// Export for use in other modules
window.SidebarComponent = SidebarComponent;
