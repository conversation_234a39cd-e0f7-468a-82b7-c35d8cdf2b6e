
<!-- Home Dashboard Section -->
<div x-show="activeNav === 'home'"
    x-transition:enter="fade-scale-enter"
    x-transition:enter-start=""
    x-transition:enter-end="fade-scale-enter-active"
    x-transition:leave="fade-scale-leave"
    x-transition:leave-start=""
    x-transition:leave-end="fade-scale-leave-active"
>
    <!-- Dashboard Stats Cards -->
    <div class="dashboard-stats">
        <div class="stat-card">
            <div class="stat-icon">
                <span class="material-icons">people</span>
            </div>
            <div class="stat-content">
                <div class="stat-number">1,247</div>
                <div class="stat-label">Total Students</div>
                <div class="stat-change positive">
                    <span class="material-icons">trending_up</span>
                    <span>+12 this month</span>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon warning">
                <span class="material-icons">payments</span>
            </div>
            <div class="stat-content">
                <div class="stat-number">€45,230</div>
                <div class="stat-label">Pending Fees</div>
                <div class="stat-change negative">
                    <span class="material-icons">trending_down</span>
                    <span>-5% from last month</span>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon success">
                <span class="material-icons">event_available</span>
            </div>
            <div class="stat-content">
                <div class="stat-number">94.2%</div>
                <div class="stat-label">Attendance Rate</div>
                <div class="stat-change positive">
                    <span class="material-icons">trending_up</span>
                    <span>+2.1% this week</span>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon info">
                <span class="material-icons">grade</span>
            </div>
            <div class="stat-content">
                <div class="stat-number">15.8</div>
                <div class="stat-label">Average Grade</div>
                <div class="stat-change positive">
                    <span class="material-icons">trending_up</span>
                    <span>+0.3 this term</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="dashboard-section">
        <div class="section-header">
            <h3 class="section-title">Quick Actions</h3>
        </div>
        <div class="quick-actions-grid">
            <button class="quick-action-card" @click="activeNav = 'students'">
                <div class="quick-action-icon">
                    <span class="material-icons">person_add</span>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">Add Student</div>
                    <div class="quick-action-subtitle">Register new student</div>
                </div>
            </button>

            <button class="quick-action-card">
                <div class="quick-action-icon">
                    <span class="material-icons">qr_code</span>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">Scan QR Code</div>
                    <div class="quick-action-subtitle">Quick attendance</div>
                </div>
            </button>

            <button class="quick-action-card">
                <div class="quick-action-icon">
                    <span class="material-icons">assessment</span>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">Generate Report</div>
                    <div class="quick-action-subtitle">Monthly summary</div>
                </div>
            </button>

            <button class="quick-action-card">
                <div class="quick-action-icon">
                    <span class="material-icons">notifications</span>
                </div>
                <div class="quick-action-content">
                    <div class="quick-action-title">Send Notice</div>
                    <div class="quick-action-subtitle">Broadcast message</div>
                </div>
            </button>
        </div>
    </div>

    <!-- Recent Activity & Upcoming Events -->
    <div class="dashboard-row">
        <div class="dashboard-column">
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">Recent Activity</h3>
                    <button class="card-action">
                        <span class="material-icons">more_vert</span>
                    </button>
                </div>
                <div class="card-content">
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">person_add</span>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">New student registered</div>
                                <div class="activity-subtitle">Sarah Johnson - Grade 10</div>
                                <div class="activity-time">2 hours ago</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">payment</span>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">Fee payment received</div>
                                <div class="activity-subtitle">€450 from Alex Martin</div>
                                <div class="activity-time">4 hours ago</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">grade</span>
                            </div>
                            <div class="activity-content">
                                <div class="activity-title">Grades updated</div>
                                <div class="activity-subtitle">Mathematics - Grade 11</div>
                                <div class="activity-time">6 hours ago</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-column">
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="card-title">Upcoming Events</h3>
                    <button class="card-action">
                        <span class="material-icons">add</span>
                    </button>
                </div>
                <div class="card-content">
                    <div class="event-list">
                        <div class="event-item">
                            <div class="event-date">
                                <div class="event-day">15</div>
                                <div class="event-month">Dec</div>
                            </div>
                            <div class="event-content">
                                <div class="event-title">Parent-Teacher Meeting</div>
                                <div class="event-time">
                                    <span class="material-icons">schedule</span>
                                    <span>2:00 PM - 5:00 PM</span>
                                </div>
                            </div>
                        </div>
                        <div class="event-item">
                            <div class="event-date">
                                <div class="event-day">18</div>
                                <div class="event-month">Dec</div>
                            </div>
                            <div class="event-content">
                                <div class="event-title">Winter Break Begins</div>
                                <div class="event-time">
                                    <span class="material-icons">event</span>
                                    <span>All Day</span>
                                </div>
                            </div>
                        </div>
                        <div class="event-item">
                            <div class="event-date">
                                <div class="event-day">22</div>
                                <div class="event-month">Dec</div>
                            </div>
                            <div class="event-content">
                                <div class="event-title">Grade Reports Due</div>
                                <div class="event-time">
                                    <span class="material-icons">assignment</span>
                                    <span>End of Day</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>