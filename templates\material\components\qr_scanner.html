<!-- QR Code Scanner Modal -->
<div class="modal-overlay" id="qr-scanner-overlay">
    <div class="modal qr-scanner-modal" id="qr-scanner-modal">
        <div class="modal-header">
            <h2 class="modal-title">QR Code Scanner</h2>
            <button class="modal-close" id="qr-scanner-close">
                <span class="material-icons">close</span>
            </button>
        </div>
        <div class="modal-content qr-scanner-content">
            <div id="qr-reader"></div>
            <div class="qr-scanner-message" id="qr-scanner-message">
                <div class="qr-scanner-icon">
                    <span class="material-icons">qr_code_scanner</span>
                </div>
                <div class="qr-scanner-text">Position the QR code within the frame to scan</div>

                <!-- Development Test Button -->
                <div class="qr-test-option" style="margin-top: 20px; display: none;" id="qr-test-option">
                    <div style="margin-bottom: 12px; color: var(--text-secondary); font-size: 14px;">
                        Development Test:
                    </div>
                    <button class="mdc-button mdc-button--outlined" id="qr-test-btn">
                        <span class="mdc-button__ripple"></span>
                        <span class="material-icons mdc-button__icon">bug_report</span>
                        <span class="mdc-button__label">Test QR Scan</span>
                    </button>
                </div>
            </div>
            
            <!-- Scanner Controls -->
            <div class="qr-scanner-controls">
                <button class="mdc-button mdc-button--outlined" id="qr-start-scan">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons">play_arrow</span>
                    <span class="mdc-button__label">Start Scanning</span>
                </button>
                <button class="mdc-button mdc-button--outlined" id="qr-stop-scan" style="display: none;">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons">stop</span>
                    <span class="mdc-button__label">Stop Scanning</span>
                </button>
                <button class="mdc-button mdc-button--outlined" id="qr-switch-camera">
                    <span class="mdc-button__ripple"></span>
                    <span class="material-icons">flip_camera_android</span>
                    <span class="mdc-button__label">Switch Camera</span>
                </button>
            </div>

            <div class="qr-scanner-result" id="qr-scanner-result" style="display: none;">
                <div class="qr-result-icon">
                    <span class="material-icons">check_circle</span>
                </div>
                <div class="qr-result-title">QR Code Scanned Successfully!</div>
                <div class="qr-result-content" id="qr-result-content"></div>
            </div>

            <!-- Error State -->
            <div class="qr-scan-error" id="qr-scan-error" style="display: none;">
                <div class="scan-error-icon">
                    <span class="material-icons">error</span>
                </div>
                <div class="scan-error-title">Scanner Error</div>
                <div class="scan-error-message" id="qr-error-message"></div>
                <button class="mdc-button mdc-button--outlined" id="qr-retry-scan">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Try Again</span>
                </button>
            </div>
            <div class="modal-actions">
                <button type="button" class="mdc-button mdc-button--outlined" id="qr-scanner-cancel">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">Cancel</span>
                </button>
                <button type="button" class="mdc-button mdc-button--raised" id="qr-scanner-ok" style="display: none;">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">OK</span>
                </button>
            </div>
        </div>
    </div>
</div>
